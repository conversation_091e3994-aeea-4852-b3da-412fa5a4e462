"""
Search Controller

This module defines the API endpoints for the searcher service.
"""

import os
from typing import Dict, Any
from fastapi import APIRouter, BackgroundTasks, HTTPException

from src.models.booking_search_request import (
    BookingSearchRequest,
    BookingSearchPeriodRequest,
    SearchStatusResponse,
)
from src.services.search_service import SearchService
from src.services.period_search_service import PeriodSearchService
from src.utils.common_utils import get_period_weeks

# Create router
router = APIRouter()

# Create search service
search_service = SearchService()

# Default output directory from environment variable or use default
DEFAULT_OUTPUT_DIR = os.getenv("BROWSER_OUTPUT_DIR", "./data/html")


@router.post("/search", response_model=Dict[str, str])
async def search(request: BookingSearchRequest, background_tasks: BackgroundTasks):
    """
    Perform a booking.com search and save the results.

    The search is performed asynchronously in the background.

    Returns:
        Dict[str, str]: A dictionary with a message indicating the search has been started.
    """
    # Add the search task to background tasks
    background_tasks.add_task(search_service.search, request, DEFAULT_OUTPUT_DIR)

    return {
        "message": f"Search started for {request.area} with {request.guests} guests for period {request.get_period_name()}"
    }


@router.post("/search-period", response_model=Dict[str, Any])
async def search_period(
    request: BookingSearchPeriodRequest, background_tasks: BackgroundTasks
):
    """
    Perform a booking.com period search across multiple weeks and save the results.

    The search is performed asynchronously in the background for all weeks in the specified period.

    Returns:
        Dict[str, str]: A dictionary with a message indicating the period search has been started.
    """
    # Calculate weeks for the period
    weeks = get_period_weeks(request.date_from, request.date_upto)

    if len(weeks) == 0:
        raise HTTPException(
            status_code=400, detail="No weeks found in the specified date range"
        )

    # Create period search service and add task to background tasks
    period_search_service = PeriodSearchService(search_service)
    background_tasks.add_task(
        period_search_service.run_period_search,
        request.area,
        request.guests,
        weeks,
        DEFAULT_OUTPUT_DIR,
        request.max_concurrent,
        None  # No progress callback for API
    )

    return {
        "message": f"Period search started for {request.area} with {request.guests} guests for period {request.get_period_name()}",
        "weeks_count": len(weeks),
        "max_concurrent": request.max_concurrent,
        "weeks": [
            {"start": start.strftime("%Y-%m-%d"), "end": end.strftime("%Y-%m-%d")}
            for start, end in weeks
        ],
    }


@router.get("/search/status", response_model=SearchStatusResponse)
async def get_search_status():
    """
    Get the status of all searches.

    Returns:
        SearchStatusResponse: A response containing a list of search statuses.
    """
    try:
        statuses = search_service.get_search_statuses()
        return SearchStatusResponse(searches=statuses)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))