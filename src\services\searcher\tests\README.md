# Searcher Service Tests

This directory contains test files for the Searcher service.

## Purpose

- Ensures the Searcher service correctly scrapes data from Booking.com
- Validates browser automation functionality
- Tests handling of various search parameters
- Verifies proper HTML storage and organization

## Expected Content

This directory will contain:
- Unit tests for search parameter handling
- Integration tests for browser automation
- Mock responses for testing without actual web requests
- Test fixtures for different search scenarios
- Performance tests for scraping efficiency
- Tests for result objects (SearchResult and PeriodSearchResult)
- Validation tests for result object structure and data integrity

## Test Guidelines

When implementing tests for this service:

### Core Functionality Tests
- Test various search parameters (locations, dates, guests)
- Verify correct handling of Booking.com's UI elements
- Test pagination and scrolling functionality
- Ensure proper storage of HTML content
- Validate error handling for failed searches
- Test retry mechanisms for intermittent failures

### Result Object Tests
- Test SearchResult object creation and field population
- Verify PeriodSearchResult aggregation and error handling
- Validate result object serialization (to_dict() methods)
- Test timing accuracy in elapsed_time fields
- Verify field order (contextual fields first: area, guests, period)
- Test result object consistency across different scenarios

### Timeout Configuration Tests
- Test page_timeout configuration across all browser classes
- Test click_timeout configuration across all browser classes
- Verify timeout values are correctly read from environment variables
- Test timeout behavior under different network conditions
- Validate timeout consistency between BrowserService, BookingBrowser, and BrowserScrapperPlaywright

### Service Integration Tests
- Test SearchService with different result scenarios
- Test PeriodSearchService with various batch sizes
- Verify progress callback functionality
- Test concurrency control and error isolation

## Running Tests

Tests should be executable via standard testing frameworks and integrated with the CI/CD pipeline to ensure service reliability.

## Notes

- Some tests may require mocking external services to avoid actual web requests
- Consider using recorded HTTP responses for consistent testing
- Include tests for the auto-scroll functionality to ensure all results are captured
