import asyncio
import sys
from datetime import datetime, timedelta
from functools import wraps


def fix_windows_event_loop_policy():
    """
    Fix Windows event loop policy to support subprocess operations.

    On Windows, the default asyncio event loop policy doesn't support subprocess
    operations which are required by <PERSON><PERSON>. This function sets the Windows
    ProactorEventLoopPolicy which does support subprocess operations.

    This should be called before any asyncio operations that might use subprocesses.
    """
    if sys.platform == "win32":
        # Set the event loop policy to support subprocess operations on Windows
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


def ensure_windows_event_loop_policy():
    """
    Ensure Windows event loop policy is set for the current thread.

    This function checks if we're on Windows and if the current event loop
    supports subprocess operations. If not, it sets the ProactorEventLoopPolicy.

    This is useful for ensuring subprocess support in background tasks or
    different execution contexts.
    """
    if sys.platform == "win32":
        try:
            # Try to get the current event loop
            loop = asyncio.get_running_loop()
            # Check if the current loop supports subprocess operations
            # by checking if it's a Proactor<PERSON>vent<PERSON>oop
            if not hasattr(loop, '_make_subprocess_transport') or \
               loop.__class__.__name__ != 'ProactorEventLoop':
                # Current loop doesn't support subprocesses, but we can't change
                # the running loop. Set the policy for future loops.
                asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        except RuntimeError:
            # No running event loop, set the policy for when one is created
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


# Helper function to run async functions with Click
def async_command(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        # Fix Windows event loop policy before running async code
        fix_windows_event_loop_policy()
        return asyncio.run(f(*args, **kwargs))

    return wrapper


# Helper function to calculate Monday and Sunday dates
def get_week_start_end(
    date: datetime | None = None, end_date_delta=7
) -> tuple[datetime, datetime]:
    """Calculate week start and end dates."""
    if not date:
        date = datetime.now()
    weekday = date.weekday()
    days_to_monday = (0 - weekday) % 7
    start = date
    if days_to_monday != 0:
        start = date - timedelta(days=7 - days_to_monday)
    end = start + timedelta(days=end_date_delta)
    return start, end


# Helper function to calculate Monday and Sunday date strings
def get_week_start_end_str(
    date: datetime | None = None, end_date_delta=7
) -> tuple[str, str]:
    """Calculate week start and end date strings."""
    start, end = get_week_start_end(date, end_date_delta)
    start_str = start.strftime("%Y-%m-%d")
    end_str = end.strftime("%Y-%m-%d")
    return start_str, end_str


# Helper function to calculate period weeks list
def get_period_weeks(
    dateFrom: datetime, dateUpto: datetime | None = None
) -> list[tuple[datetime, datetime]]:
    """Calculate period weeks list."""
    weeks = []
    date = dateFrom
    start, end = get_week_start_end(date)
    weeks.append((start, end))
    while end < dateUpto:
        date = end + timedelta(days=1)
        start, end = get_week_start_end(date)
        weeks.append((start, end))
    return weeks
