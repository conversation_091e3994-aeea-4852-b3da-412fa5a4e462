"""
Browser Service

This module provides the service for browser automation.
"""

import os
import random
import logging
from datetime import datetime

from playwright.async_api import async_playwright, <PERSON>, TimeoutError as PlaywrightTimeoutError

from src.models.booking_search_request import BookingSearchRequest
from src.utils.common_utils import fix_windows_event_loop_policy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Booking.com base URL
BOOKING_BASE_URL = "https://www.booking.com"


class BrowserService:
    """
    Service for browser automation.
    """

    def __init__(self):
        """
        Initialize the browser service.
        """
        # User agent list for anti-detection
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        ]

        # Configure browser settings
        self.headless = os.getenv("BROWSER_HEADLESS", "false").lower() == "true"
        self.min_delay = int(os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MIN", "200"))
        self.max_delay = int(os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MAX", "1000"))
        self.page_timeout = int(os.getenv("BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS", "10000"))
        self.click_timeout = int(os.getenv("BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS", "2000"))

    async def get_booking_content(self, request: BookingSearchRequest) -> str:
        """
        Perform a booking.com search and return the HTML content.

        Args:
            request: The search request

        Returns:
            str: The HTML content of the search results page
        """
        # Fix Windows event loop policy before using Playwright
        fix_windows_event_loop_policy()

        async with async_playwright() as playwright:
            # Launch browser with anti-detection measures
            browser = await playwright.chromium.launch(
                headless=self.headless,
                args=[
                    "--disable-blink-features=AutomationControlled",
                    "--disable-features=IsolateOrigins,site-per-process",
                    "--disable-site-isolation-trials",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    # Additional args for Xvfb compatibility
                    "--use-gl=egl",
                    "--mute-audio",
                ]
            )

            # Create a new browser context with a random user agent
            user_agent = random.choice(self.user_agents)
            context = await browser.new_context(
                viewport={"width": 1280, "height": 800},
                user_agent=user_agent,
                locale="en-US",
                timezone_id="Europe/Athens",
                has_touch=False,
                java_script_enabled=True,
                ignore_https_errors=False,
                extra_http_headers={
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Connection": "keep-alive"
                }
            )

            # Add script to hide webdriver
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });
            """)

            # Create a new page
            page = await context.new_page()

            try:
                # Navigate to Booking.com
                await page.goto(BOOKING_BASE_URL)
                await page.wait_for_load_state("networkidle", timeout=self.page_timeout)

                # Accept cookies if dialog appears
                try:
                    await page.click('button[id="onetrust-accept-btn-handler"]', timeout=self.click_timeout)
                    logger.info("Accepted cookies")
                except PlaywrightTimeoutError:
                    logger.info("No cookie dialog found or already accepted")

                # Create search URL
                neighborhood = request.area.split(',')[0].strip()
                direct_url = (
                    f"{BOOKING_BASE_URL}/searchresults.html"
                    f"?ss={neighborhood}"
                    f"&checkin={request.check_in}"
                    f"&checkout={request.check_out}"
                    f"&group_adults={request.guests}"
                    "&group_children=0"
                    "&no_rooms=1"
                    "&selected_currency=EUR"
                    "&nflt=entire_place_bedroom_count%3D2"  # Default to 2 bedrooms
                )

                logger.info(f"Using direct URL: {direct_url}")

                # Navigate to search results
                await page.goto(direct_url)
                await page.wait_for_load_state("networkidle", timeout=self.page_timeout)
                await self._random_delay(page)

                # Try to expand filters
                await self._expand_filters(page)
                await self._random_delay(page)

                # Scroll down to load all results
                await self._scroll_page(page)
                await self._random_delay(page)

                # Get page content
                content = await page.content()

                # Return the content
                return content

            finally:
                # Close browser
                await browser.close()

    async def _random_delay(self, page: Page, min_seconds: float = 0.5, max_seconds: float = 2.0):
        """
        Wait for a random amount of time.

        Args:
            page: The page to wait on
            min_seconds: Minimum wait time in seconds
            max_seconds: Maximum wait time in seconds
        """
        delay = random.uniform(min_seconds, max_seconds)
        await page.wait_for_timeout(delay * 1000)

    async def _expand_filters(self, page: Page):
        """
        Try to expand filters to show more options.

        Args:
            page: The page to interact with
        """
        try:
            # Look for "Show more" buttons in filters
            show_more_buttons = await page.query_selector_all('button:has-text("Show more")')
            for button in show_more_buttons:
                await button.click(timeout=self.click_timeout)
                await self._random_delay(page, 0.2, 0.5)
        except Exception as e:
            logger.warning(f"Error expanding filters: {e}")

    async def _scroll_page(self, page: Page, max_scrolls: int = 30):
        """
        Scroll down the page to load all results.

        Booking.com uses lazy loading for search results, so we need to scroll
        down to load all results.

        Args:
            page: The page to scroll
            max_scrolls: Maximum number of scrolls to perform
        """
        try:
            # Get initial property count
            previous_count = 0
            current_count = await self._get_property_count(page)

            logger.info(f"Initial property count: {current_count}")

            # Method 1: Progressive scrolling with dynamic waits
            scroll_count = 0
            no_change_count = 0

            # First, scroll to the bottom quickly to trigger any lazy loading
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await self._random_delay(page, 2.0, 3.0)

            # Then do incremental scrolling
            while scroll_count < max_scrolls and no_change_count < 3:
                previous_count = current_count

                # Calculate a random scroll amount (between 100px and full viewport height)
                scroll_amount = random.randint(300, 800)
                await page.evaluate(f"window.scrollBy(0, {scroll_amount})")

                # Wait for network to be idle to ensure content is loaded
                try:
                    await page.wait_for_load_state("networkidle", timeout=self.page_timeout)
                except PlaywrightTimeoutError:
                    # If timeout, just continue with a delay
                    pass

                await self._random_delay(page, 1.0, 2.0)

                # Check if more properties were loaded
                current_count = await self._get_property_count(page)

                logger.info(f"Scroll {scroll_count + 1}: Property count now {current_count}")
                scroll_count += 1

                # If count didn't change, increment no_change_count
                if current_count == previous_count:
                    no_change_count += 1
                else:
                    no_change_count = 0

                    # If we found more results, scroll to bottom again
                    if scroll_count % 3 == 0:
                        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                        await self._random_delay(page, 1.5, 2.5)

            # Method 2: Look for and click pagination elements
            try:
                # Try to find pagination elements (next page buttons)
                pagination_selectors = [
                    'button[aria-label="Next page"]',
                    'a.bui-pagination__link[aria-label="Next page"]',
                    'a.paging-next',
                    'a.js-pagination-next-link',
                    'li.bui-pagination__item--next a',
                    'button[data-testid="pagination-next"]',
                    'button.show_more',
                    'button[data-testid="show-more-results-button"]',
                    'a:has-text("Next")',
                    'a:has-text("Show more")',
                    'button:has-text("Show more")',
                    'button:has-text("Load more")',
                    'button:has-text("Next")'
                ]

                for selector in pagination_selectors:
                    pagination_element = await page.query_selector(selector)
                    if pagination_element:
                        logger.info(f"Found pagination element with selector: {selector}")

                        # Check if the element is visible and enabled
                        is_visible = await pagination_element.is_visible()
                        if is_visible:
                            logger.info("Pagination element is visible, clicking it")
                            await pagination_element.click(timeout=self.click_timeout)
                            await self._random_delay(page, 3.0, 5.0)

                            # Wait for network to be idle
                            try:
                                await page.wait_for_load_state("networkidle", timeout=self.page_timeout)
                            except PlaywrightTimeoutError:
                                pass

                            # Check if more properties were loaded
                            new_count = await self._get_property_count(page)
                            logger.info(f"After clicking pagination: Property count now {new_count}")

                            # If we got more results, do some more scrolling
                            if new_count > current_count:
                                await self._scroll_after_pagination(page)

                            break
            except Exception as e:
                logger.warning(f"Error handling pagination: {e}")

            # Method 3: Final thorough scrolling
            await self._final_thorough_scrolling(page)

            # One more check for final count
            final_count = await self._get_property_count(page)
            logger.info(f"Final property count after all scrolling methods: {final_count}")

        except Exception as e:
            logger.warning(f"Error during page scrolling: {e}")

    async def _scroll_after_pagination(self, page: Page):
        """
        Perform additional scrolling after pagination to load all new content.

        Args:
            page: The page to scroll
        """
        try:
            # Scroll to top first
            await page.evaluate("window.scrollTo(0, 0)")
            await self._random_delay(page, 1.0, 1.5)

            # Then scroll down in increments
            viewport_height = await page.evaluate("window.innerHeight")
            page_height = await page.evaluate("document.body.scrollHeight")

            current_position = 0
            while current_position < page_height:
                # Scroll down by viewport height
                current_position += viewport_height
                await page.evaluate(f"window.scrollTo(0, {current_position})")
                await self._random_delay(page, 0.8, 1.2)
        except Exception as e:
            logger.warning(f"Error during post-pagination scrolling: {e}")

    async def _final_thorough_scrolling(self, page: Page):
        """
        Perform a final thorough scrolling of the page to ensure all content is loaded.

        Args:
            page: The page to scroll
        """
        try:
            # First scroll to the very bottom
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await self._random_delay(page, 2.0, 3.0)

            # Then scroll up in increments
            page_height = await page.evaluate("document.body.scrollHeight")
            step = page_height / 10  # Divide page into 10 sections

            # Scroll from bottom to top in steps
            for i in range(10, 0, -1):
                position = i * step
                await page.evaluate(f"window.scrollTo(0, {position})")
                await self._random_delay(page, 0.5, 0.8)

            # Then back to bottom
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await self._random_delay(page, 1.0, 2.0)

            # Try one more time with a different approach - smooth scrolling
            await page.evaluate("""
                (function() {
                    const height = document.body.scrollHeight;
                    const duration = 3000; // ms
                    const start = window.pageYOffset;
                    const startTime = performance.now();

                    function scroll(timestamp) {
                        const elapsed = timestamp - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        const ease = function(t) { return t<.5 ? 4*t*t*t : (t-1)*(2*t-2)*(2*t-2)+1; }; // easeInOutCubic
                        window.scrollTo(0, start + (height - start) * ease(progress));

                        if (progress < 1) {
                            window.requestAnimationFrame(scroll);
                        }
                    }

                    window.requestAnimationFrame(scroll);
                })();
            """)

            # Wait for the smooth scrolling to complete
            await self._random_delay(page, 3.5, 4.5)
        except Exception as e:
            logger.warning(f"Error during final thorough scrolling: {e}")

    async def _get_property_count(self, page: Page) -> int:
        """
        Get the number of property cards currently loaded on the page.

        Args:
            page: The page to check

        Returns:
            int: The number of property cards
        """
        try:
            # Try multiple selectors that might match Booking.com's property cards
            # The structure of the page might change over time, so we try different selectors
            selectors = [
                '[data-testid="property-card"]',
                '.sr_property_block',
                '.sr_item',
                '.js-sr-card',
                '.bui-card',
                '.a826ba81c4',  # This is a common class for property cards in recent Booking.com versions
                '.fe621a2f0f',  # Another common class for property cards
                '.ef2936e71e',  # Another possible class
                '[data-hotelid]',  # Properties often have a data-hotelid attribute
                '.b978843432',  # Another possible class for property cards
                '.d4924c9e74',  # Another possible class for property cards
                '.c90a25d457',  # Another possible class for property cards
                '.b2b5147b20',  # Another possible class for property cards
                '.d20f4628d0',  # Another possible class for property cards
                '.b_property_card',  # Older class name
                '.property_card',  # Older class name
                '.hotel_card',  # Older class name
                '.accommodation-list-card',  # Possible class name
                '.accommodation-card',  # Possible class name
                '.property-card-container',  # Possible class name
                '.property-card',  # Possible class name
                '.hotel-card',  # Possible class name
                '.hotel-item',  # Possible class name
                '.hotel_item',  # Possible class name
                '.hotel-result',  # Possible class name
                '.hotel_result',  # Possible class name
                '.search-result',  # Possible class name
                '.search_result',  # Possible class name
                '.result-item',  # Possible class name
                '.result_item'   # Possible class name
            ]

            # Try each selector and use the one that returns the most elements
            max_count = 0
            used_selector = None

            for selector in selectors:
                elements = await page.query_selector_all(selector)
                count = len(elements)
                if count > max_count:
                    max_count = count
                    used_selector = selector

            if max_count > 0:
                logger.info(f"Found {max_count} properties using selector: {used_selector}")
            else:
                logger.warning("Could not find any properties with known selectors")

            return max_count

        except Exception as e:
            logger.warning(f"Error counting properties: {e}")
            return 0

    def _save_content(self, content: str, output_dir: str, sub_dir: str = None) -> str:
        """
        Save HTML content to file with timestamp.

        Args:
            content: The HTML content to save
            output_dir: The base output directory
            sub_dir: Optional subdirectory

        Returns:
            str: The path to the saved file
        """
        # Create directory path
        dir_path = output_dir
        if sub_dir:
            dir_path = os.path.join(dir_path, sub_dir)

        # Create directory if it doesn't exist
        os.makedirs(dir_path, exist_ok=True)

        # Create file path with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(dir_path, f"{timestamp}.html")

        # Write content to file
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)

        return file_path
