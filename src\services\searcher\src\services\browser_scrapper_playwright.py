"""
Browser Scrapper Playwright

This module provides a Playwright implementation of the browser scrapper.
"""

import os
import random
from playwright.async_api import async_playwright

from src.services.browser_scrapper_abstract import B<PERSON><PERSON><PERSON><PERSON>rapperAbstract
from src.stores.browser_scrapper_store_abstract import B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Abstract
from src.utils.common_utils import fix_windows_event_loop_policy


class BrowserScrapperPlaywright(BrowserScrapperAbstract):
    """
    Playwright implementation of the browser scrapper.
    """

    def __init__(self, url: str, store: BrowserScrapperStoreAbstract) -> None:
        """
        Initialize the BrowserScrapperPlaywright with a URL and a store.

        Args:
            url: The URL to scrape
            store: The store to save the results to
        """
        super().__init__(url, store)

        # Configure browser timeout settings
        self.page_timeout = int(os.getenv("BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS", "10000"))
        self.click_timeout = int(os.getenv("BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS", "2000"))

    async def download_page_content(self) -> str:
        """
        Download page content string using Playwright browser.

        Returns:
            str: The HTML content of the page
        """
        # Fix Windows event loop policy before using Playwright
        fix_windows_event_loop_policy()

        async with async_playwright() as p:
            # Launch browser with Docker compatibility flags
            headless = os.getenv("BROWSER_HEADLESS", "false").lower() == "true"
            browser = await p.chromium.launch(
                headless=headless,
                args=["--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu"],
            )
            try:
                page = await browser.new_page()
                await page.goto(self.url)
                # Wait for network activity to settle
                await page.wait_for_timeout(self.delay_milliseconds)
                await page.wait_for_load_state("networkidle", timeout=self.page_timeout)
                await page.wait_for_timeout(self.delay_milliseconds)
                content = await page.content()
                return content
            finally:
                await browser.close()

    @property
    def delay_milliseconds(self) -> float:
        """
        Get the delay in milliseconds.

        Returns:
            float: The delay in milliseconds
        """
        min_delay = float(
            os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MIN", "200")
        )
        max_delay = float(
            os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MAX", "1000")
        )
        return random.uniform(min_delay, max_delay)
