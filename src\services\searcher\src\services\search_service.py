"""
Search Service

This module provides the service for performing booking.com searches.
"""

import os
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from src.models.booking_search_request import BookingSearchRequest, SearchStatus
from src.services.browser_service import BrowserService
from src.factories.browser_scrapper_factory import BrowserScrapperFactory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class SearchResult:
    """Result of a search operation."""

    def __init__(self, area: str, guests: int, period: str, success: bool, output_path: Optional[str], elapsed_time: float, error: Optional[str] = None):
        self.area = area
        self.guests = guests
        self.period = period
        self.success = success
        self.output_path = output_path
        self.elapsed_time = elapsed_time
        self.error = error

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "area": self.area,
            "guests": self.guests,
            "period": self.period,
            "success": self.success,
            "output_path": self.output_path,
            "elapsed_time": self.elapsed_time,
            "error": self.error
        }


class SearchService:
    """
    Service for performing booking.com searches.
    """

    def __init__(self):
        """
        Initialize the search service.
        """
        self.browser_service = BrowserService()
        self.factory = BrowserScrapperFactory()
        self.search_statuses = []

    async def search(self, request: BookingSearchRequest, output_dir: str) -> SearchResult:
        """
        Perform a booking.com search and save the results.

        Args:
            request: The search request
            output_dir: The directory to save the results to

        Returns:
            SearchResult: The result of the search operation
        """
        # Record start time
        start_time = time.time()

        logger.info(f"Starting search for {request.area} with {request.guests} guests for period {request.get_period_name()}")

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Add search to statuses
        status_id = len(self.search_statuses)
        self.search_statuses.append({
            "id": str(status_id),
            "area": request.area,
            "guests": str(request.guests),
            "period": request.get_period_name(),
            "status": "in_progress",
            "started_at": datetime.now().isoformat(),
            "completed_at": "",
            "output_path": "",
        })

        try:
            # Perform the search using the factory
            booking_scrapper = self.factory.create_booking_search_scrapper(request, output_dir)
            output_path = await booking_scrapper.download_page(request.get_sub_directory())

            # Update search status
            self.search_statuses[status_id].update({
                "status": "completed",
                "completed_at": datetime.now().isoformat(),
                "output_path": output_path or "",
            })

            # Publish event to notify other services
            await self._publish_search_completed_event(request, output_path)

            # Calculate elapsed time and create result
            elapsed_time = time.time() - start_time
            result = SearchResult(
                area=request.area,
                guests=request.guests,
                period=request.get_period_name(),
                success=True,
                output_path=output_path,
                elapsed_time=elapsed_time
            )

            logger.info(f"Search completed for {request.area} with {request.guests} guests for period {request.get_period_name()} in {elapsed_time:.2f} seconds")
            return result

        except Exception as e:
            # Update search status
            self.search_statuses[status_id].update({
                "status": "failed",
                "completed_at": datetime.now().isoformat(),
                "error": str(e),
            })

            # Calculate elapsed time and create error result
            elapsed_time = time.time() - start_time
            result = SearchResult(
                area=request.area,
                guests=request.guests,
                period=request.get_period_name(),
                success=False,
                output_path=None,
                elapsed_time=elapsed_time,
                error=str(e)
            )

            logger.error(f"Search failed for {request.area} with {request.guests} guests for period {request.get_period_name()} in {elapsed_time:.2f} seconds: {e}")
            return result

    def get_search_statuses(self) -> List[SearchStatus]:
        """
        Get the status of all searches.

        Returns:
            List[SearchStatus]: A list of search statuses.
        """
        # Convert dict objects to SearchStatus objects
        return [SearchStatus(**status) for status in self.search_statuses]

    async def _publish_search_completed_event(self, request: BookingSearchRequest, output_path: str):
        """
        Publish an event to notify other services that a search has completed.

        Args:
            request: The search request
            output_path: The path to the saved search results
        """
        # TODO: Implement event publishing to Azure Service Bus
        logger.info(f"Publishing search completed event for {request.area} with {request.guests} guests for period {request.get_period_name()}")
