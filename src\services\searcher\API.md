# Searcher Service API Documentation

This document provides comprehensive documentation for the Searcher Service REST API.

## Base URL

When running locally: `http://localhost:8000`
When running in Docker: `http://localhost:8000`

## API Endpoints

### 1. Start Single Search

**Endpoint:** `POST /api/v1/search`

**Description:** Initiates a single booking.com search for a specific date range.

**Request Body:**
```json
{
  "area": "Psiri",
  "guests": 4,
  "check_in": "2025-06-01",
  "check_out": "2025-06-08"
}
```

**Request Model:** `BookingSearchRequest`
- `area` (string, required): The area to search for
- `guests` (integer, optional): Number of guests (default: 2)
- `check_in` (string, required): Check-in date in YYYY-MM-DD format
- `check_out` (string, required): Check-out date in YYYY-MM-DD format

**Response:**
```json
{
  "message": "Search started for <PERSON><PERSON><PERSON> with 4 guests for period 2025-06-01 to 2025-06-08"
}
```

**Status Codes:**
- `200 OK`: Search started successfully
- `422 Unprocessable Entity`: Invalid request data
- `500 Internal Server Error`: Server error

### 2. Start Period Search

**Endpoint:** `POST /api/v1/search-period`

**Description:** Initiates multiple searches across a date range, automatically splitting into weekly periods.

**Request Body:**
```json
{
  "area": "Psiri",
  "guests": 2,
  "date_from": "2025-06-01T00:00:00",
  "date_upto": "2025-06-30T23:59:59",
  "max_concurrent": 2
}
```

**Request Model:** `BookingSearchPeriodRequest`
- `area` (string, required): The area to search for
- `guests` (integer, optional): Number of guests (default: 2)
- `date_from` (datetime, required): Start date of the period
- `date_upto` (datetime, required): End date of the period
- `max_concurrent` (integer, optional): Maximum concurrent searches (default: 2)

**Response:**
```json
{
  "message": "Period search started for Psiri with 2 guests for period 2025-06-01 to 2025-06-30",
  "weeks_count": 5,
  "max_concurrent": 2,
  "weeks": [
    {"start": "2025-05-26", "end": "2025-06-02"},
    {"start": "2025-06-02", "end": "2025-06-09"},
    {"start": "2025-06-09", "end": "2025-06-16"},
    {"start": "2025-06-16", "end": "2025-06-23"},
    {"start": "2025-06-23", "end": "2025-06-30"}
  ]
}
```

**Status Codes:**
- `200 OK`: Period search started successfully
- `400 Bad Request`: No weeks found in the specified date range
- `422 Unprocessable Entity`: Invalid request data
- `500 Internal Server Error`: Server error

### 3. Get Search Status

**Endpoint:** `GET /api/v1/search/status`

**Description:** Retrieves the status of all searches (both single and period searches).

**Request:** No request body required.

**Response Model:** `SearchStatusResponse`
```json
{
  "searches": [
    {
      "id": "0",
      "area": "Psiri",
      "guests": "4",
      "period": "2025-06-01 to 2025-06-08",
      "status": "completed",
      "started_at": "2025-05-25T17:54:10.143450",
      "completed_at": "2025-05-25T17:55:35.209000",
      "output_path": "/app/data/html/Psiri/4/2025-06-01_2025-06-08/20250525_175535.html",
      "error": null
    },
    {
      "id": "1",
      "area": "Psiri",
      "guests": "2",
      "period": "2025-07-01 to 2025-07-08",
      "status": "in_progress",
      "started_at": "2025-05-25T17:54:31.874262",
      "completed_at": "",
      "output_path": "",
      "error": null
    },
    {
      "id": "2",
      "area": "Psiri",
      "guests": "2",
      "period": "2025-08-01 to 2025-08-08",
      "status": "failed",
      "started_at": "2025-05-25T17:56:15.123456",
      "completed_at": "2025-05-25T17:56:45.987654",
      "output_path": "",
      "error": "Connection timeout after 30 seconds"
    }
  ]
}
```

**Status Codes:**
- `200 OK`: Status retrieved successfully
- `500 Internal Server Error`: Server error

## Data Models

### SearchStatus

Represents the status of an individual search operation.

**Fields:**
- `id` (string): Unique identifier for the search
- `area` (string): The area being searched
- `guests` (string): Number of guests (as string for API consistency)
- `period` (string): Search period in "YYYY-MM-DD to YYYY-MM-DD" format
- `status` (string): Current status of the search
- `started_at` (string): ISO timestamp when search was started
- `completed_at` (string, optional): ISO timestamp when search completed (empty if in progress)
- `output_path` (string, optional): Path to saved results (empty if not completed)
- `error` (string, optional): Error message if search failed (null if no error)

### SearchStatusResponse

Wrapper for the search status endpoint response.

**Fields:**
- `searches` (array): List of SearchStatus objects

### Status Values

The `status` field in SearchStatus can have the following values:

- **`in_progress`**: Search is currently running
- **`completed`**: Search finished successfully with results saved
- **`failed`**: Search encountered an error and could not complete

## Interactive Documentation

The service provides interactive API documentation via Swagger UI:

**URL:** `http://localhost:8000/docs`

This interface allows you to:
- Explore all available endpoints
- View detailed request/response schemas
- Test API calls directly from the browser
- Download OpenAPI specification

## Error Handling

All endpoints return structured error responses for validation and server errors:

**Validation Error (422):**
```json
{
  "detail": [
    {
      "type": "missing",
      "loc": ["body", "area"],
      "msg": "Field required",
      "input": {}
    }
  ]
}
```

**Server Error (500):**
```json
{
  "detail": "Internal server error message"
}
```

## Rate Limiting

The service implements internal rate limiting through:
- Configurable delays between browser actions
- Maximum concurrent search limits
- Automatic retry mechanisms for failed requests

## Authentication

Currently, the API does not require authentication. This may be added in future versions.

## CORS

Cross-Origin Resource Sharing (CORS) is enabled for development purposes. Configure appropriately for production use.
