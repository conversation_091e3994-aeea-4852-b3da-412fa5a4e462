# Booking Models

This directory contains data models specific to Booking.com integration.

## Purpose

- Defines data structures for Booking.com entities
- Models the structure of Booking.com search requests and responses
- Provides type definitions for Booking.com-specific data
- Ensures consistent representation of Booking.com data

## Expected Content

This directory will contain:
- Model classes for Booking.com entities
- Request and response DTOs
- Type definitions
- Validation rules
- Mapping utilities
- Serialization/deserialization helpers

## Model Types

Typical models in this directory may include:
- BookingSearchRequest: Parameters for a Booking.com search
- BookingProperty: Representation of a property listing
- BookingRoom: Details of room offerings
- BookingPrice: Price structure with taxes and fees
- BookingAmenity: Property and room amenities
- BookingReview: Review and rating information
- BookingLocation: Geographical and address information

## Guidelines

When implementing Booking.com models:
- Follow the structure of Booking.com's data where appropriate
- Include all relevant fields from the source data
- Document fields with clear descriptions
- Implement validation for required fields
- Consider versioning for compatibility with API changes
- Include mapping functions for transformations

## Notes

- Models should accurately represent Booking.com's data structure
- Consider backward compatibility when evolving models
- Document any assumptions or special handling
- Include examples of model usage where helpful
- Keep models updated with changes to Booking.com's structure
