"""
Browser Scrapper Factory

This module provides a factory for creating browser scrapper instances.
"""

import importlib
import os
from typing import List, Any

from src.models.booking_search_request import BookingSearchRequest
from src.services.booking_search_scrapper import BookingSearchScrapper


class BrowserScrapperFactory:
    """
    Factory class to create class instances for browser scrapping.
    """

    def create_browser_scrapper(self, url: str, output_dir: str) -> Any:
        """
        Create a browser scrapper instance.

        Args:
            url: The URL to scrape
            output_dir: The directory to save the results to

        Returns:
            BrowserScrapperAbstract: A browser scrapper instance
        """
        implementation = self._get_class_definition_from_environment(
            "BROWSER_SCRAPPER_IMPLEMENTATION",
            "src.services.browser_service,BrowserService",
        )
        module_name = implementation[0]
        class_name = implementation[1]
        scrapper = self.create_object_from_module(
            module_name,
            class_name,
            url,
            self.create_browser_scrapper_store(output_dir),
        )
        return scrapper

    def create_booking_search_scrapper(
        self, request: BookingSearchRequest, output_dir: str
    ) -> BookingSearchScrapper:
        """
        Create a booking search scrapper instance.

        Args:
            request: The booking search request
            output_dir: The directory to save the results to

        Returns:
            BookingSearchScrapper: A booking search scrapper instance
        """
        scrapper = BookingSearchScrapper(
            request,
            self.create_browser_scrapper_store(output_dir),
        )
        return scrapper

    def create_browser_scrapper_store(self, output_dir: str) -> Any:
        """
        Create a browser scrapper store instance.

        Args:
            output_dir: The directory to save the results to

        Returns:
            BrowserScrapperStoreAbstract: A browser scrapper store instance
        """
        implementation = self._get_class_definition_from_environment(
            "BROWSER_STORE_IMPLEMENTATION",
            "src.stores.browser_scrapper_store_path,BrowserScrapperStorePath",
        )
        module_name = implementation[0]
        class_name = implementation[1]
        store = self.create_object_from_module(module_name, class_name, output_dir)
        return store

    def _get_class_definition_from_environment(
        self, env_var: str, default: str
    ) -> List[str]:
        """
        Get the class definition from the environment variable or use the default.

        Args:
            env_var: Environment variable name to check
            default: Default class definition if env_var is not set

        Returns:
            List[str]: A list containing the module name and class name
        """
        implementation = os.getenv(env_var, default)
        if not implementation:
            raise ValueError(f"{env_var} not set")
        implementation_details = implementation.split(",")
        if len(implementation_details) != 2:
            raise ValueError(
                f"{env_var} must be in the format 'module_name,class_name'"
            )
        module_name = implementation_details[0].strip()
        if not module_name:
            raise ValueError(f"{env_var} module name not set")
        class_name = implementation_details[1].strip()
        if not class_name:
            raise ValueError(f"{env_var} class name not set")
        return [module_name, class_name]

    def _load_module(self, module_name: str):
        """
        Dynamically load a Python module using its namespace path.

        Args:
            module_name: Fully qualified module name (e.g., "stores.browser_scrapper_store_path")

        Returns:
            module: The loaded Python module

        Raises:
            ImportError: If the module cannot be loaded
        """
        try:
            module = importlib.import_module(module_name)
            return module
        except ImportError as e:
            raise ImportError(f"Could not load module {module_name}: {str(e)}") from e

    def create_object_from_module(
        self, module_name: str, class_name: str, *args, **kwargs
    ):
        """
        Create an object instance from a specified module and class name.

        Args:
            module_name: Fully qualified module name (e.g., "stores.browser_scrapper_store_path")
            class_name: Name of the class to instantiate
            *args: Positional arguments to pass to the class constructor
            **kwargs: Keyword arguments to pass to the class constructor

        Returns:
            object: Instance of the specified class

        Raises:
            ImportError: If the module cannot be loaded
            AttributeError: If the class doesn't exist in the module
        """
        # Load the module
        module = self._load_module(module_name)

        # Get and validate the class from the module
        if not hasattr(module, class_name):
            raise AttributeError(
                f"Class '{class_name}' not found in module '{module_name}'"
            )

        # Get the class and create an instance
        class_type = getattr(module, class_name)
        return class_type(*args, **kwargs)
