# Searcher Service

This service is responsible for scraping booking.com for rental unit data. It provides both a REST API and a CLI interface.

## Features

- **REST API** for performing booking.com searches with structured responses
- **Type-safe Pydantic models** for all API requests and responses
- **Real-time search status tracking** with detailed progress information
- **Enhanced CLI interface** with multiple commands:
  - Single search for a specific week
  - Period search for multiple weeks within a date range
  - Automatic week boundary calculation (Monday to Sunday)
  - Configurable concurrency control for period searches
- **Background task processing** with async execution
- **Event publishing** for notifying other services
- **Configurable browser automation** with anti-detection measures
- **Flexible storage options**:
  - Local file system storage
  - Azure Storage integration
- **Docker support** with Xvfb for headless and non-headless modes
- **Modular service architecture**:
  - Dedicated PeriodSearchService for multi-week searches
  - Reusable components across CLI and API
  - Progress callback system for different UI contexts
- **Utility functions** for date handling and period calculations
- **Enhanced logging** with booking-specific logger
- **Modern dependency management** with uv package manager

## Installation

### Prerequisites

- Python 3.11+
- uv package manager
- Playwright
- Docker (optional, for containerized deployment)

### Setup with Virtual Environment (uv)

1. Create a virtual environment using uv:

   **Linux/macOS (Bash):**
   ```bash
   # Create a virtual environment in the default .venv directory
   uv venv

   # Activate the virtual environment (optional)
   source .venv/bin/activate
   ```

   **Windows (PowerShell):**
   ```powershell
   # Create a virtual environment in the default .venv directory
   uv venv

   # Activate the virtual environment (optional)
   .venv\Scripts\Activate.ps1
   ```

2. Install dependencies using uv:
   ```bash
   # Install from pyproject.toml
   uv pip install -e .

   # Or sync with locked dependencies (most reproducible)
   uv sync
   ```

3. Install Playwright browsers:

   **With activated environment:**
   ```bash
   # Linux/macOS/Windows (same commands)
   playwright install chromium
   playwright install-deps
   ```

   **Without activation (using uv run):**
   ```bash
   # Linux/macOS/Windows (same commands)
   uv run playwright install chromium
   uv run playwright install-deps
   ```

### Setup with Docker (Recommended)

1. Build and run the Docker container:

   **Linux/macOS (Bash):**
   ```bash
   # Build the Docker image
   docker build -t airprice-searcher .

   # Run the container with a name for easier management
   docker run -d \
     --name searcher-service \
     -p 8000:8000 \
     -v $(pwd)/data:/app/data \
     airprice-searcher
   ```

   **Windows (PowerShell):**
   ```powershell
   # Build the Docker image
   docker build -t airprice-searcher .

   # Run the container with a name for easier management
   docker run -d `
     --name searcher-service `
     -p 8000:8000 `
     -v "${PWD}/data:/app/data" `
     airprice-searcher
   ```

   This will:
   - Build the Docker image with all dependencies (uv, Playwright, Chromium, Xvfb)
   - Start the container in detached mode with Xvfb for browser automation
   - Map port 8000 to your host machine
   - Mount the data directory for persistent storage

2. View logs:
   ```bash
   docker logs -f searcher-service
   ```

3. Stop the container:
   ```bash
   docker stop searcher-service
   docker rm searcher-service
   ```

### Alternative Docker Setup with Custom Environment

For custom configurations, you can run the container with environment variables:

**Linux/macOS (Bash):**
```bash
# Run with custom environment variables
docker run -d \
  --name searcher-custom \
  -p 8000:8000 \
  -v "${PWD}/data:/app/data" \
  -e "BROWSER_HEADLESS=true" \
  -e "BOOKING_AREA=Psiri" \
  -e "BOOKING_GUESTS=4" \
  -e "BROWSER_MAX_CONCURRENT=1" \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Run with custom environment variables
docker run -d `
  --name searcher-custom `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  -e "BROWSER_HEADLESS=true" `
  -e "BOOKING_AREA=Psiri" `
  -e "BOOKING_GUESTS=4" `
  -e "BROWSER_MAX_CONCURRENT=1" `
  airprice-searcher
```

### Development Mode with Docker

For development, mount the source code to make changes without rebuilding:

**Linux/macOS (Bash):**
```bash
# Development mode with source code mounting
docker run -d \
  --name searcher-dev \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/src:/app/src \
  -e "BROWSER_HEADLESS=false" \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Development mode with source code mounting
docker run -d `
  --name searcher-dev `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  -v "${PWD}/src:/app/src" `
  -e "BROWSER_HEADLESS=false" `
  airprice-searcher
```

See [DOCKER.md](DOCKER.md) for more detailed Docker instructions and troubleshooting.

## Usage

### Running the API Server

```bash
# Using activated environment
python -m src.main serve

# Or using uv run without activation
uv run python -m src.main serve
```

The API server will be available at http://localhost:8000.

For comprehensive API documentation, see [API.md](API.md).

### API Endpoints

- `POST /api/v1/search`: Perform a booking.com search
  ```json
  {
    "area": "Psiri",
    "guests": 2,
    "check_in": "2025-06-01",
    "check_out": "2025-06-30"
  }
  ```

- `POST /api/v1/search-period`: Perform a booking.com period search across multiple weeks
  ```json
  {
    "area": "Psiri",
    "guests": 2,
    "date_from": "2025-06-01",
    "date_upto": "2025-06-30",
    "max_concurrent": 2
  }
  ```

- `GET /api/v1/search/status`: Get the status of all searches

#### API Response Examples

**Single Search Response:**
```json
{
  "message": "Search started for Psiri with 2 guests for period 2025-06-01 to 2025-06-30"
}
```

**Period Search Response:**
```json
{
  "message": "Period search started for Psiri with 2 guests for period 2025-06-01 to 2025-06-30",
  "weeks_count": 5,
  "max_concurrent": 2,
  "weeks": [
    {"start": "2025-05-26", "end": "2025-06-02"},
    {"start": "2025-06-02", "end": "2025-06-09"},
    {"start": "2025-06-09", "end": "2025-06-16"},
    {"start": "2025-06-16", "end": "2025-06-23"},
    {"start": "2025-06-23", "end": "2025-06-30"}
  ]
}
```

**Search Status Response:**
```json
{
  "searches": [
    {
      "id": "0",
      "area": "Psiri",
      "guests": "4",
      "period": "2025-06-01 to 2025-06-08",
      "status": "completed",
      "started_at": "2025-05-25T17:54:10.143450",
      "completed_at": "2025-05-25T17:55:35.209000",
      "output_path": "/app/data/html/Psiri/4/2025-06-01_2025-06-08/20250525_175535.html",
      "error": null
    },
    {
      "id": "1",
      "area": "Psiri",
      "guests": "2",
      "period": "2025-07-01 to 2025-07-08",
      "status": "in_progress",
      "started_at": "2025-05-25T17:54:31.874262",
      "completed_at": "",
      "output_path": "",
      "error": null
    },
    {
      "id": "2",
      "area": "Psiri",
      "guests": "2",
      "period": "2025-08-01 to 2025-08-08",
      "status": "failed",
      "started_at": "2025-05-25T17:56:15.123456",
      "completed_at": "2025-05-25T17:56:45.987654",
      "output_path": "",
      "error": "Connection timeout after 30 seconds"
    }
  ]
}
```

## API Models

The service uses type-safe Pydantic models for all API interactions, ensuring data validation and clear documentation.

### Request Models

#### BookingSearchRequest
```python
{
  "area": str,           # Required: The area to search for
  "guests": int,         # Optional: Number of guests (default: 2)
  "check_in": str,       # Required: Check-in date in YYYY-MM-DD format
  "check_out": str       # Required: Check-out date in YYYY-MM-DD format
}
```

#### BookingSearchPeriodRequest
```python
{
  "area": str,           # Required: The area to search for
  "guests": int,         # Optional: Number of guests (default: 2)
  "date_from": datetime, # Required: Start date of the period
  "date_upto": datetime, # Required: End date of the period
  "max_concurrent": int  # Optional: Maximum concurrent searches (default: 2)
}
```

### Response Models

#### SearchStatus
```python
{
  "id": str,             # Unique identifier for the search
  "area": str,           # The area being searched
  "guests": str,         # Number of guests (as string)
  "period": str,         # Search period in "YYYY-MM-DD to YYYY-MM-DD" format
  "status": str,         # Current status: "in_progress", "completed", "failed"
  "started_at": str,     # ISO timestamp when search was started
  "completed_at": str,   # ISO timestamp when search completed (empty if in progress)
  "output_path": str,    # Path to saved results (empty if not completed)
  "error": str | null    # Error message if search failed
}
```

#### SearchStatusResponse
```python
{
  "searches": List[SearchStatus]  # List of all search statuses
}
```

### Status Values

- **`in_progress`**: Search is currently running
- **`completed`**: Search finished successfully with results saved
- **`failed`**: Search encountered an error and could not complete

## Result Objects

The service returns structured result objects that provide comprehensive information about search operations:

### SearchResult

Individual searches return a `SearchResult` object with the following structure:

```json
{
  "area": "Psiri",
  "guests": 2,
  "period": "2025-06-01 to 2025-06-08",
  "success": true,
  "output_path": "/app/data/html/Psiri/2/2025-06-01_2025-06-08/20250127_103045.html",
  "elapsed_time": 27.34,
  "error": null
}
```

**Fields:**
- **`area`**: The search location
- **`guests`**: Number of guests for the search
- **`period`**: Human-readable date range (YYYY-MM-DD to YYYY-MM-DD)
- **`success`**: Boolean indicating if the search was successful
- **`output_path`**: File path where results are saved (null if failed)
- **`elapsed_time`**: Time taken for the search in seconds
- **`error`**: Error message if the search failed (null if successful)

### PeriodSearchResult

Period searches return a `PeriodSearchResult` object with the following structure:

```json
{
  "total_weeks": 4,
  "successful": 3,
  "failed": 1,
  "errors": ["Week 2/4 failed for 2025-06-08 to 2025-06-15: Connection timeout"],
  "elapsed_time": 89.67
}
```

**Fields:**
- **`total_weeks`**: Total number of weeks processed
- **`successful`**: Number of successful searches
- **`failed`**: Number of failed searches
- **`errors`**: List of error messages for failed searches
- **`elapsed_time`**: Total time taken for all searches in seconds

### Using the CLI

```bash
# Using activated environment
# Perform a search (automatically calculates current week)
python -m src.main search --area "Psiri" --guests 2

# Perform a search with specific dates
python -m src.main search --area "Psiri" --guests 4 --check-in "2025-06-01" --check-out "2025-06-30"

# Perform a period search (multiple weeks within a date range)
python -m src.main search-period --area "Psiri" --guests 2 --date-from "2025-06-01" --date-upto "2025-06-30"

# Period search with custom concurrency
python -m src.main search-period --area "Psiri" --guests 2 --date-from "2025-06-01" --date-upto "2025-06-30" --max-concurrent 5

# Using environment variables
export BROWSER_MAX_CONCURRENT=5
export BOOKING_AREA="Paris"
python -m src.main search-period --guests 2 --date-from "2025-06-01" --date-upto "2025-06-30"

# Or using uv run without activation
uv run python -m src.main search --area "Psiri" --guests 2
uv run python -m src.main search --area "Psiri" --guests 2 --check-in "2025-06-01" --check-out "2025-06-30"
uv run python -m src.main search-period --area "Psiri" --guests 2 --date-from "2025-06-01" --date-upto "2025-06-30"
```

#### CLI Commands

- **`serve`**: Start the API server
- **`search`**: Perform a single booking.com search for a week period
  - Automatically calculates week boundaries (Monday to Sunday) if no dates provided
  - Uses current date's week if no check-in date specified
- **`search-period`**: Perform multiple searches across a date range
  - Breaks down the period into weekly searches
  - Configurable concurrency control (--max-concurrent option)
  - Progress reporting with success/failure indicators
  - Useful for bulk data collection across multiple weeks

### Example Output

```bash
# Individual search with enhanced result information
$ python -m src.main search --area "Psiri" --guests 2
Search completed for Psiri with 2 guests for period 2025-01-27 to 2025-02-03 in 27.34 seconds
Results saved to: /app/data/html/Psiri/2/2025-01-27_2025-02-03/20250127_103045.html

# Failed search example
$ python -m src.main search --area "InvalidArea" --guests 2
Search failed for InvalidArea with 2 guests for period 2025-01-27 to 2025-02-03 in 5.23 seconds
Error: Connection timeout after 30 seconds

# Period search with detailed progress and summary
$ python -m src.main search-period --area "Psiri" --guests 2 --date-from "2025-06-01" --date-upto "2025-06-30"
Starting period search for Psiri with 2 guests from 2025-06-01 to 2025-06-30
Week 1/5: Searching for 2025-06-01 to 2025-06-08... ✓
Week 2/5: Searching for 2025-06-08 to 2025-06-15... ✓
Week 3/5: Searching for 2025-06-15 to 2025-06-22... ✓
Week 4/5: Searching for 2025-06-22 to 2025-06-29... ✓
Week 5/5: Searching for 2025-06-29 to 2025-07-06... ✓
Period search completed: 5/5 successful searches in 127.45 seconds
```

## Configuration

The service can be configured using environment variables in a `.env` file or by setting them directly when running the Docker container:

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | API server port | 8000 |
| HOST | API server host | 0.0.0.0 |
| BROWSER_HEADLESS | Run browser in headless mode | false |
| BROWSER_SCRAPPER_IMPLEMENTATION | Browser scrapper implementation | src.services.browser_scrapper_playwright,BrowserScrapperPlaywright |
| BROWSER_STORE_IMPLEMENTATION | Browser store implementation | src.stores.browser_scrapper_store_path,BrowserScrapperStorePath |
| BROWSER_OUTPUT_DIR | Directory to save HTML content | ./data/html |
| BROWSER_DELAY_IN_MILLISECONDS_MIN | Minimum delay between actions | 200 |
| BROWSER_DELAY_IN_MILLISECONDS_MAX | Maximum delay between actions | 1000 |
| BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS | Timeout for page operations | 10000 |
| BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS | Timeout for click operations | 2000 |
| BROWSER_MAX_CONCURRENT | Maximum number of concurrent searches | 2 |
| BOOKING_AREA | Default area to search | Psiri |
| BOOKING_GUESTS | Default number of guests | 2 |
| BOOKING_CHECK_IN | Check-in date (YYYY-MM-DD) | Auto-calculated |
| BOOKING_CHECK_OUT | Check-out date (YYYY-MM-DD) | Auto-calculated |
| AZURE_STORAGE_CONNECTION_STRING | Azure Storage connection string | None |
| AZURE_STORAGE_FILE_SHARE_NAME | Azure Storage file share name | None |

See `.env.example` for a template of environment variables.

## Docker

### Docker Features

- **uv Package Manager**: Fast Python package management and dependency resolution
- **Xvfb Support**: Virtual display server for running browsers in headless environments
- **Playwright & Chromium**: Pre-installed browser automation tools with all dependencies
- **System Dependencies**: All required system packages for browser automation
- **Automatic Startup**: Xvfb starts automatically before the application
- **Volume Mounting**: Support for data persistence and development mode
- **Environment Variables**: Comprehensive configuration through environment variables

### Building and Running

**Linux/macOS (Bash):**
```bash
# Build the Docker image
docker build -t airprice-searcher .

# Run the container with basic configuration
docker run -d \
  --name searcher-service \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  airprice-searcher

# Run with custom environment variables
docker run -d \
  --name searcher-custom \
  -p 8000:8000 \
  -v "${PWD}/data:/app/data" \
  -e "BROWSER_HEADLESS=true" \
  -e "BOOKING_AREA=Psiri" \
  -e "BOOKING_GUESTS=4" \
  -e "BROWSER_MAX_CONCURRENT=1" \
  airprice-searcher
```

**Windows (PowerShell):**
```powershell
# Build the Docker image
docker build -t airprice-searcher .

# Run the container with basic configuration
docker run -d `
  --name searcher-service `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  airprice-searcher

# Run with custom environment variables
docker run -d `
  --name searcher-custom `
  -p 8000:8000 `
  -v "${PWD}/data:/app/data" `
  -e "BROWSER_HEADLESS=true" `
  -e "BOOKING_AREA=Psiri" `
  -e "BOOKING_GUESTS=4" `
  -e "BROWSER_MAX_CONCURRENT=1" `
  airprice-searcher
```

For more detailed Docker instructions and troubleshooting, see [DOCKER.md](DOCKER.md).

## Development

### Project Structure

```
searcher/
├── src/
│   ├── controllers/       # API controllers
│   │   └── search_controller.py    # Search endpoints
│   ├── factories/         # Factory classes
│   │   └── browser_scrapper_factory.py  # Browser factory
│   ├── models/            # Data models
│   │   ├── booking/       # Booking.com specific models
│   │   └── booking_search_request.py  # Search request/response models
│   ├── services/          # Business logic
│   │   ├── search_service.py        # Core search service
│   │   ├── period_search_service.py # Multi-week search service
│   │   ├── browser_service.py       # Browser automation
│   │   └── booking_search_scrapper.py # Booking.com scrapper
│   ├── stores/            # Storage implementations
│   │   ├── browser_scrapper_store_path.py   # Local file storage
│   │   └── browser_scrapper_store_azure.py  # Azure storage
│   ├── utils/             # Utility functions
│   │   ├── booking_logger.py    # Logging utilities
│   │   └── common_utils.py      # Common utility functions
│   └── main.py            # Entry point with CLI commands
├── config/                # Configuration files
├── data/                  # Data storage directory
│   └── html/              # Downloaded HTML content
├── tests/                 # Unit tests (placeholder directory)
├── .env.example           # Example environment variables
├── docker-build.sh        # Docker build and push script for Azure
├── Dockerfile             # Docker configuration
├── API.md                 # Comprehensive API documentation
├── DOCKER.md              # Docker documentation
├── MIGRATION.md           # Migration guide for uv package manager
├── pyproject.toml         # Project metadata and dependencies
└── uv.lock                # Lock file for reproducible builds
```

### Running Tests

Currently, the tests directory contains only a placeholder README.md file. To implement tests:

```bash
# Install development dependencies
uv sync --group dev

# Run tests when implemented
uv run pytest

# Or using uvx (runs in isolated environment)
uvx pytest

# Or using the test module directly
uv run python -m unittest discover tests
```

See `tests/README.md` for guidelines on implementing tests for this service.

### Code Quality

The project includes several code quality tools configured in `pyproject.toml`:

#### Remove Unused Imports
```bash
# Check for unused imports (with activated environment)
autoflake --remove-all-unused-imports --recursive --check src

# Remove unused imports (with activated environment)
autoflake --remove-all-unused-imports --recursive --in-place src

# Or using uv run
uv run autoflake --remove-all-unused-imports --recursive --check src
uv run autoflake --remove-all-unused-imports --recursive --in-place src

# Or using uvx
uvx autoflake --remove-all-unused-imports --recursive --check src
uvx autoflake --remove-all-unused-imports --recursive --in-place src
```

#### Code Formatting
```bash
# Format code with Black
uv run black src/
uvx black src/

# Sort imports with isort
uv run isort src/
uvx isort src/
```

#### Install Development Dependencies
```bash
# Install with development dependencies
uv pip install -e ".[dev]"

# Or sync with development dependencies
uv sync --group dev
```

### Dependency Management

```bash
# Lock dependencies to uv.lock file
uv lock

# Update all dependencies to latest versions
uv lock --upgrade

# Sync environment with locked dependencies
uv sync

# Add a new dependency
uv add <package-name>

# Add a development dependency
uv add --dev <package-name>

# Add with version constraint
uv add 'requests==2.31.0'
```

For more detailed information about the migration to uv package manager, see [MIGRATION.md](MIGRATION.md).

### Benefits of uv Package Manager

- **Faster Installations**: Up to 10-100x faster than pip
- **Reproducible Builds**: Lock files ensure consistent environments
- **Better Caching**: Improved Docker build times
- **Modern Tooling**: Aligns with current Python packaging best practices

### Utility Functions

The project includes several utility functions in `src/utils/common_utils.py`:

- **`get_week_start_end(date)`**: Calculate the start (Monday) and end (Sunday) of a week for a given date
- **`get_week_start_end_str(date)`**: Get week boundaries as formatted strings (YYYY-MM-DD)
- **`get_period_weeks(date_from, date_upto)`**: Calculate all week periods within a date range
- **`async_command()`**: Decorator for creating async Click commands

These utilities support the enhanced CLI functionality, particularly the automatic week boundary calculation and period search features.

### Service Architecture

The project follows a modular service-oriented architecture:

#### Core Services

- **`SearchService`**: Handles individual booking.com searches
  - Manages search lifecycle with real-time status tracking
  - Provides structured SearchStatus objects with detailed progress information
  - Integrates with browser automation and storage
  - Publishes events for completed searches
  - Returns type-safe SearchResult objects

- **`PeriodSearchService`**: Orchestrates multi-week searches
  - Implements concurrency control with configurable limits
  - Provides progress callback system for different UI contexts
  - Handles batch processing with error isolation
  - Reusable across CLI and API interfaces

#### Design Benefits

- **DRY Principle**: Eliminates code duplication between CLI and API
- **Separation of Concerns**: Business logic separated from presentation
- **Testability**: Services can be unit tested independently
- **Flexibility**: Progress callbacks allow different UI implementations
- **Maintainability**: Changes to search logic centralized in services

#### Progress Callback System

The `PeriodSearchService` supports optional progress callbacks:

```python
def my_progress_callback(week_num: int, total_weeks: int, period_name: str, success: Optional[bool]):
    # success: None=starting, True=completed, False=failed
    if success is None:
        print(f"Starting week {week_num}/{total_weeks}: {period_name}")
    elif success:
        print(f"✓ Completed week {week_num}/{total_weeks}")
    else:
        print(f"✗ Failed week {week_num}/{total_weeks}")

# Usage
service = PeriodSearchService()
result = await service.run_period_search(
    area="Psiri", guests=2, weeks=weeks_list,
    output_dir="./data", max_concurrent=2,
    progress_callback=my_progress_callback
)
```

## Azure Storage Integration

The service supports storing HTML content in Azure Storage instead of the local file system. To use this feature:

1. Set the following environment variables:
   ```
   BROWSER_STORE_IMPLEMENTATION="src.stores.browser_scrapper_store_azure,BrowserScrapperStoreAzure"
   AZURE_STORAGE_CONNECTION_STRING="<Your Azure Storage connection string>"
   AZURE_STORAGE_FILE_SHARE_NAME="<Your Azure file share name>"
   ```

2. For local development, you can use Azurite (Azure Storage Emulator).

### Using Azurite for Local Development

#### Manual Installation
1. Install Azurite:
   ```bash
   npm install -g azurite
   ```

2. Run Azurite:
   ```bash
   azurite --silent --location ./azurite --debug ./azurite/debug.log
   ```

3. Set your connection string to:
   ```
   AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"
   ```

## Recent Updates

- **API Response Models**: Enhanced API with type-safe Pydantic models (2025-05-25)
  - **SearchStatus Model**: Type-safe model for search status responses with proper field validation
  - **SearchStatusResponse Model**: Structured wrapper for status endpoint responses
  - **Real-time Status Tracking**: Enhanced search service with detailed progress information and status updates
  - **Fixed Validation Issues**: Resolved FastAPI response validation errors with proper type conversions
  - **Comprehensive API Documentation**: New API.md file with detailed endpoint and model documentation
- **Result Objects**: Enhanced search operations with structured result objects
  - **SearchResult**: Individual searches now return comprehensive result objects with area, guests, period, success status, output path, elapsed time, and error information
  - **PeriodSearchResult**: Period searches return detailed summaries with total weeks, success/failure counts, error details, and elapsed time
  - **Consistent Structure**: Both result types provide complete context and performance metrics
  - **Field Order**: Contextual fields (area, guests, period) appear first, followed by technical fields (success, output_path, elapsed_time, error)
- **Timeout Configuration**: Enhanced browser timeout management
  - **Page Timeout**: New `BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS` (default: 10000ms) for page operations
  - **Click Timeout**: Renamed from `BROWSER_SELECTOR_TIMEOUT_IN_MILLISECONDS` to `BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS` (default: 2000ms)
  - **Unified Implementation**: All browser classes (BrowserService, BookingBrowser, BrowserScrapperPlaywright) use consistent timeout configuration
- **Default Area**: Changed from "London" to "Psiri" as the default search area
- **Max Concurrent**: Reduced default concurrent searches from 3 to 2 for better stability
- **Package Manager**: Migrated to uv package manager for faster dependency management (10-100x faster than pip)
- **Docker Optimization**: Improved Docker build process with uv integration and better environment variable handling
- **Documentation**: Updated Docker documentation to reflect actual project structure (removed docker-compose references)
- **Container Management**: Enhanced Docker setup with named containers and better development mode support
