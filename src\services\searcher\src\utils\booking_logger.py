"""
Logger Utility

Handles logging for the BookingSpy system with both file and console output.
Provides specialized logging for search progress, processing status, and statistics.
"""
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

class BookingLogger:
    """Custom logger for the BookingSpy system"""

    def __init__(self, log_dir: str = "logs", name: str = "BookingSpy"):
        """
        Initialize logger with custom formatting and handlers.
        
        Args:
            log_dir: Directory for log files (default: "logs")
            name: Logger name (default: "BookingSpy")
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_formatter = logging.Formatter('%(message)s')
        
        # Set up file handler with date-based filename
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = self.log_dir / f"booking_spy_{current_time}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Set up console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

    def get_log_dir(self) -> Path:
        """Get the log directory path"""
        return self.log_dir

    def log_search_start(self, area: str, check_in: str, check_out: str, num_persons: int):
        """Log the start of a search operation"""
        msg = (f"\n{'='*50}\n"
               f"Starting search for:\n"
               f"Area: {area}\n"
               f"Period: {check_in} to {check_out}\n"
               f"Number of persons: {num_persons}\n"
               f"{'='*50}")
        self.logger.info(msg)

    def log_search_results(self, area: str, total_properties: int, matching_properties: int):
        """Log search results statistics"""
        msg = (f"\nSearch Results for {area}:\n"
               f"Total properties found: {total_properties}\n"
               f"Properties matching criteria: {matching_properties}\n"
               f"{'='*50}")
        self.logger.info(msg)

    def log_missing_combinations(self, missing: List[Dict]):
        """Log information about missing search combinations"""
        if missing:
            msg = "\nMissing search combinations:\n"
            for combo in missing:
                msg += (f"- Area: {combo['area']}, "
                       f"Period: {combo['check_in']} to {combo['check_out']}, "
                       f"Persons: {combo['num_persons']}\n")
        else:
            msg = "\nAll search combinations completed successfully."
        self.logger.info(f"{msg}\n{'='*50}")

    def log_processing_start(self, file_path: str):
        """Log the start of processing a search results file"""
        msg = (f"\n{'='*50}\n"
               f"Processing file: {file_path}\n"
               f"{'='*50}")
        self.logger.info(msg)

    def log_missing_data(self, apartment_name: str, missing_fields: List[str]):
        """Log information about missing apartment data"""
        msg = (f"Missing data for apartment '{apartment_name}':\n"
               f"Missing fields: {', '.join(missing_fields)}")
        self.logger.info(msg)

    def log_data_update(self, apartment_name: str, updated_fields: Dict):
        """Log information about updated apartment data"""
        msg = (f"Updated data for apartment '{apartment_name}':\n"
               f"Updated fields: {', '.join(f'{k}: {v}' for k, v in updated_fields.items())}")
        self.logger.info(msg)

    def log_processing_statistics(self, total_files: int, processed_files: int, 
                                total_apartments: int, updated_apartments: int):
        """Log processing statistics"""
        msg = (f"\nProcessing Statistics:\n"
               f"Files processed: {processed_files}/{total_files}\n"
               f"Apartments found: {total_apartments}\n"
               f"Apartments updated: {updated_apartments}\n"
               f"{'='*50}")
        self.logger.info(msg)

    def log_error(self, error_msg: str, details: Optional[str] = None):
        """Log error messages"""
        msg = f"ERROR: {error_msg}"
        if details:
            msg += f"\nDetails: {details}"
        self.logger.error(f"{msg}\n{'='*50}")

    def log_success(self, message: str):
        """Log success messages"""
        self.logger.info(f"SUCCESS: {message}")

    def log_progress(self, current: int, total: int, prefix: str = "Progress"):
        """Log progress information"""
        percentage = (current / total) * 100 if total > 0 else 0
        msg = f"{prefix}: {current}/{total} ({percentage:.1f}%)"
        self.logger.info(msg)

    def log_step_completion(self, step_name: str, success: bool, details: Optional[str] = None):
        """Log step completion status"""
        status = "completed successfully" if success else "failed"
        msg = f"\nStep '{step_name}' {status}"
        if details:
            msg += f"\nDetails: {details}"
        if success:
            self.logger.info(f"{msg}\n{'='*50}")
        else:
            self.logger.error(f"{msg}\n{'='*50}")